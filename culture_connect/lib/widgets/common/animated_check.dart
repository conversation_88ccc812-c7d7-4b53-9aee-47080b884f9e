import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A widget that displays an animated check mark
class Animated<PERSON>heck extends StatefulWidget {
  /// The size of the check mark
  final double size;

  /// The color of the check mark
  final Color color;

  /// The duration of the animation
  final Duration duration;

  /// The stroke width of the check mark
  final double strokeWidth;

  /// Creates a new animated check mark
  const AnimatedCheck({
    super.key,
    this.size = 100.0,
    this.color = Colors.green,
    this.duration = const Duration(milliseconds: 1000),
    this.strokeWidth = 5.0,
  });

  @override
  State<AnimatedCheck> createState() => _AnimatedCheckState();
}

class _AnimatedCheckState extends State<AnimatedCheck>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.size,
      width: widget.size,
      decoration: BoxDecoration(
        color: widget.color.withAlpha(30),
        shape: BoxShape.circle,
      ),
      child: Padding(
        padding: EdgeInsets.all(widget.size * 0.2),
        child: AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return CustomPaint(
              painter: CheckPainter(
                value: _animation.value,
                color: widget.color,
                strokeWidth: widget.strokeWidth,
              ),
            );
          },
        ),
      ),
    );
  }
}

/// A custom painter for drawing the check mark
class CheckPainter extends CustomPainter {
  /// The progress value of the animation (0.0 to 1.0)
  final double value;

  /// The color of the check mark
  final Color color;

  /// The stroke width of the check mark
  final double strokeWidth;

  /// Creates a new check painter
  CheckPainter({
    required this.value,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCapound
      ..strokeJoin = StrokeJoinound;

    final path = Path();

    // Calculate the points for the check mark
    final double startX = sizeidth * 0.2;
    final double midX = sizeidth * 0.4;
    final double endX = sizeidth * 0.8;

    final double startY = sizeeight * 0.5;
    final double midY = sizeeight * 0.7;
    final double endY = sizeeight * 0.3;

    // First part of the check mark (down-stroke)
    if (value < 0.5) {
      final progress = math.min(1.0, value * 2);

      path.moveTo(startX, startY);
      path.lineTo(
        startX + (midX - startX) * progress,
        startY + (midY - startY) * progress,
      );
    } else {
      path.moveTo(startX, startY);
      path.lineTo(midX, midY);

      // Second part of the check mark (up-stroke)
      final progress = math.max(0.0, (value - 0.5) * 2);

      path.lineTo(
        midX + (endX - midX) * progress,
        midY + (endY - midY) * progress,
      );
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CheckPainter oldDelegate) {
    return oldDelegate.value != value ||
        oldDelegate.color != color ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}
