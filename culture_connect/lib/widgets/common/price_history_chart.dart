import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/price_alert.dart';
import 'package:culture_connect/theme/app_colors.dart';

/// A widget that displays a price history chart
class PriceHistoryChart extends StatelessWidget {
  /// The price history data
  final List<PriceHistoryPoint> priceHistory;

  /// The target price
  final double targetPrice;

  /// The currency
  final String currency;

  /// Whether to show the target price line
  final bool showTargetPrice;

  /// Whether to show the grid
  final bool showGrid;

  /// Whether to show the tooltip
  final bool showTooltip;

  /// Creates a new price history chart
  const PriceHistoryChart({
    super.key,
    required this.priceHistory,
    required this.targetPrice,
    required this.currency,
    this.showTargetPrice = true,
    this.showGrid = true,
    this.showTooltip = true,
  });

  @override
  Widget build(BuildContext context) {
    // Sort the price history by date
    final sortedHistory = List<PriceHistoryPoint>.from(priceHistory)
      ..sort((a, b) => a.date.compareTo(b.date));

    // Find min and max prices
    double minPrice = double.infinity;
    double maxPrice = 0;

    for (final point in sortedHistory) {
      if (point.price < minPrice) {
        minPrice = point.price;
      }
      if (point.price > maxPrice) {
        maxPrice = point.price;
      }
    }

    // Include target price in min/max calculation
    if (showTargetPrice) {
      if (targetPrice < minPrice) {
        minPrice = targetPrice;
      }
      if (targetPrice > maxPrice) {
        maxPrice = targetPrice;
      }
    }

    // Add some padding to min/max
    final pricePadding = (maxPrice - minPrice) * 0.1;
    minPrice = (minPrice - pricePadding).clamp(0, double.infinity);
    maxPrice = maxPrice + pricePadding;

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: showGrid,
          drawVerticalLine: showGrid,
          horizontalInterval: (maxPrice - minPrice) / 4,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.withAlpha(51),
              strokeWidth: 1,
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.grey.withAlpha(51),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: _getDateInterval(sortedHistory),
              getTitlesWidget: (value, meta) {
                if (value < 0 || value >= sortedHistory.length) {
                  return const SizedBox();
                }

                final date = sortedHistory[value.toInt()].date;
                return Padding(
                  padding: EdgeInsets.only(top: 8.0),
                  child: Text(
                    DateFormat('MM/dd').format(date),
                    style: const TextStyle(
                      color: Colors.grey,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: (maxPrice - minPrice) / 4,
              getTitlesWidget: (value, meta) {
                return Padding(
                  padding: EdgeInsets.only(right: 8.0),
                  child: Text(
                    '$currency${value.toStringAsFixed(0)}',
                    style: const TextStyle(
                      color: Colors.grey,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                );
              },
              reservedSize: 40,
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: Colors.grey.withAlpha(51)),
        ),
        minX: 0,
        maxX: sortedHistory.length - 1.0,
        minY: minPrice,
        maxY: maxPrice,
        lineTouchData: LineTouchData(
          enabled: showTooltip,
          touchTooltipData: LineTouchTooltipData(
            tooltipBgColor: Colors.blueGreyithAlpha(204),
            getTooltipItems: (touchedSpots) {
              return touchedSpots.map((spot) {
                final index = spot.x.toInt();
                if (index < 0 || index >= sortedHistory.length) {
                  return null;
                }

                final point = sortedHistory[index];
                return LineTooltipItem(
                  '${DateFormat('MM/dd/yyyy').format(point.date)}\n',
                  const TextStyle(
                    color: Colorshite,
                    fontWeight: FontWeight.bold,
                  ),
                  children: [
                    TextSpan(
                      text: '$currency${point.price.toStringAsFixed(2)}',
                      style: const TextStyle(
                        color: Colorshite,
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                  ],
                );
              }).toList();
            },
          ),
        ),
        lineBarsData: [
          // Price history line
          LineChartBarData(
            spots: List.generate(sortedHistory.length, (index) {
              return FlSpot(
                index.toDouble(),
                sortedHistory[index].price,
              );
            }),
            isCurved: true,
            color: AppColors.primary,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: const FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: AppColors.primaryithAlpha(51),
            ),
          ),
          // Target price line
          if (showTargetPrice)
            LineChartBarData(
              spots: [
                FlSpot(0, targetPrice),
                FlSpot(sortedHistory.length - 1.0, targetPrice),
              ],
              isCurved: false,
              color: AppColors.success,
              barWidth: 2,
              isStrokeCapRound: true,
              dotData: const FlDotData(show: false),
              dashArray: [5, 5],
            ),
        ],
      ),
    );
  }

  /// Get the interval for date labels
  double _getDateInterval(List<PriceHistoryPoint> history) {
    if (history.length <= 5) {
      return 1;
    } else if (history.length <= 10) {
      return 2;
    } else if (history.length <= 20) {
      return 4;
    } else {
      return (history.length / 5).floorToDouble();
    }
  }
}
